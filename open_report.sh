#!/bin/bash

# Find the latest HTML report (check both sequential and parallel execution reports)
PARALLEL_REPORT=$(find test_framework/reports -name "test_execution_report_*.html" -type f | sort | tail -n 1)
SEQUENTIAL_REPORT=$(find test_framework/reports -name "test_report_*.html" -type f | sort | tail -n 1)

# Determine which report is newer
LATEST_REPORT=""
if [ -n "$PARALLEL_REPORT" ] && [ -n "$SEQUENTIAL_REPORT" ]; then
    # Both exist, find the newer one
    if [ "$PARALLEL_REPORT" -nt "$SEQUENTIAL_REPORT" ]; then
        LATEST_REPORT="$PARALLEL_REPORT"
        REPORT_TYPE="Parallel Execution"
    else
        LATEST_REPORT="$SEQUENTIAL_REPORT"
        REPORT_TYPE="Sequential Execution"
    fi
elif [ -n "$PARALLEL_REPORT" ]; then
    LATEST_REPORT="$PARALLEL_REPORT"
    REPORT_TYPE="Parallel Execution"
elif [ -n "$SEQUENTIAL_REPORT" ]; then
    LATEST_REPORT="$SEQUENTIAL_REPORT"
    REPORT_TYPE="Sequential Execution"
fi

if [ -z "$LATEST_REPORT" ]; then
    echo "❌ No HTML reports found in test_framework/reports/"
    echo "Run a test first with:"
    echo "  Sequential: ./run_gherkin.sh @TC1"
    echo "  Parallel:   ./run_scaled.sh @TC1"
    exit 1
fi

echo "📊 Opening latest $REPORT_TYPE report: $LATEST_REPORT"

# Open the report in the default browser
if command -v open &> /dev/null; then
    # macOS
    open "$LATEST_REPORT"
elif command -v xdg-open &> /dev/null; then
    # Linux
    xdg-open "$LATEST_REPORT"
elif command -v start &> /dev/null; then
    # Windows
    start "$LATEST_REPORT"
else
    echo "❌ Could not open browser automatically"
    echo "Please open manually: $LATEST_REPORT"
fi

echo "✅ Report opened in browser!" 