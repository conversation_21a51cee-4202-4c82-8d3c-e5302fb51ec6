# User Features - Markdown Format

## Feature: User Registration Test

### Scenario: New User Registration
- buka halaman registrasi
- ketik "<PERSON>" di field nama
- ketik "<EMAIL>" di field email
- ketik "SecurePass123" di field password
- ketik "SecurePass123" di field konfirmasi password
- klik tombol daftar
- pastikan halaman sukses registrasi muncul

### Scenario: Registration with Existing Email
- buka halaman registrasi
- ketik "<PERSON>" di field nama
- ketik "<EMAIL>" di field email
- ketik "NewPass456" di field password
- ketik "NewPass456" di field konfirmasi password
- klik tombol daftar
- pastikan pesan error "Email sudah terdaftar" muncul

## Feature: Profile Management

### Scenario: Update User Profile
- buka halaman utama
- klik "Profil Saya"
- ketik "Updated Name" di field nama
- ketik "Jakarta" di field kota
- klik tombol simpan
- pastikan pesan "Profil berhasil diperbarui" muncul

### Scenario: Upload Profile Picture
- buka halaman profil
- klik "Unggah Foto"
- pilih file gambar
- klik tombol upload
- pastikan foto profil berhasil diunggah 