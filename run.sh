#!/bin/bash
# Activate virtual environment
source .venv/bin/activate

# Initialize variables
test_cases=""
other_args=""
model="pattern_matching"  # No AI model needed

# Process all arguments
for arg in "$@"; do
    if [[ $arg == @* ]]; then
        # Remove @ prefix and add to test cases
        test_case="${arg#@}"
        if [ -z "$test_cases" ]; then
            test_cases="--test-cases $test_case"
        else
            test_cases="$test_cases $test_case"
        fi
    else
        # Add to other arguments
        if [ -z "$other_args" ]; then
            other_args="$arg"
        else
            other_args="$other_args $arg"
        fi
    fi
done

echo "✅ Starting AI test framework in original mode"
echo "💡 No AI interaction - pattern matching only"

# Run the test framework with processed arguments
python test_framework/main.py --excel test_framework/tests.xlsx --output ./test_framework/test_recordings --model $model $test_cases $other_args