#!/bin/bash
# Enhanced Smart Gherkin Generator Script
# Converts Excel test cases to Gherkin feature files with intelligent coverage enhancement

# Activate virtual environment
source .venv/bin/activate

# Default values
EXCEL_FILE="test_framework/tests.xlsx"
OUTPUT_DIR="test_framework/features"
MAX_ENHANCEMENTS=2
ENABLE_ENHANCEMENT=true
GENERATE_REPORT=false
BACKUP_EXISTING=true

# Function to show usage
show_usage() {
    echo "🚀 Enhanced Smart Gherkin Generator"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --excel FILE              Excel file path (default: test_framework/tests.xlsx)"
    echo "  --output DIR              Output directory (default: test_framework/features)"
    echo "  --max-enhancements NUM    Maximum enhancements per test case (default: 2)"
    echo "  --no-enhance              Disable coverage enhancement"
    echo "  --report                  Generate coverage report"
    echo "  --no-backup               Don't backup existing feature files"
    echo "  --help                    Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Generate with default settings"
    echo "  $0 --excel my_tests.xlsx              # Use custom Excel file"
    echo "  $0 --no-enhance                       # Generate without enhancements"
    echo "  $0 --max-enhancements 3 --report      # More enhancements + report"
    echo "  $0 --no-backup                        # Don't backup existing files"
    echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --excel)
            EXCEL_FILE="$2"
            shift 2
            ;;
        --output)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        --max-enhancements)
            MAX_ENHANCEMENTS="$2"
            shift 2
            ;;
        --no-enhance)
            ENABLE_ENHANCEMENT=false
            shift
            ;;
        --report)
            GENERATE_REPORT=true
            shift
            ;;
        --no-backup)
            BACKUP_EXISTING=false
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            echo "❌ Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Check if Excel file exists
if [[ ! -f "$EXCEL_FILE" ]]; then
    echo "❌ Excel file not found: $EXCEL_FILE"
    exit 1
fi

# Backup existing feature files if requested
if [[ "$BACKUP_EXISTING" == true ]] && [[ -d "$OUTPUT_DIR" ]]; then
    BACKUP_DIR="backup_features_$(date +%Y%m%d_%H%M%S)"
    echo "📦 Backing up existing feature files to: $BACKUP_DIR"
    mkdir -p "$BACKUP_DIR"
    cp "$OUTPUT_DIR"/*.feature "$BACKUP_DIR/" 2>/dev/null || echo "No existing feature files to backup"
fi

# Build command
CMD="python generate_gherkin_enhanced.py --excel \"$EXCEL_FILE\" --output \"$OUTPUT_DIR\" --max-enhancements $MAX_ENHANCEMENTS"

if [[ "$ENABLE_ENHANCEMENT" == false ]]; then
    CMD="$CMD --no-enhance"
fi

if [[ "$GENERATE_REPORT" == true ]]; then
    CMD="$CMD --report"
fi

# Show configuration
echo "🎯 Enhanced Smart Gherkin Generator Configuration:"
echo "   📄 Excel file: $EXCEL_FILE"
echo "   📁 Output directory: $OUTPUT_DIR"
echo "   🔧 Enhancements: $ENABLE_ENHANCEMENT"
echo "   📊 Max enhancements: $MAX_ENHANCEMENTS"
echo "   📋 Generate report: $GENERATE_REPORT"
echo "   💾 Backup existing: $BACKUP_EXISTING"
echo ""

# Run the generator
echo "🚀 Starting enhanced generation..."
eval $CMD

# Check if generation was successful
if [[ $? -eq 0 ]]; then
    echo ""
    echo "✅ Enhanced generation completed successfully!"
    echo "📁 Check the generated files in: $OUTPUT_DIR"
    
    # Show summary of generated files
    if [[ -d "$OUTPUT_DIR" ]]; then
        echo ""
        echo "📊 Generated Files Summary:"
        echo "   Feature files: $(ls -1 "$OUTPUT_DIR"/*.feature 2>/dev/null | wc -l)"
        echo "   Total scenarios: $(grep -c "Scenario:" "$OUTPUT_DIR"/*.feature 2>/dev/null || echo "0")"
        echo ""
        echo "📋 Generated Feature Files:"
        ls -1 "$OUTPUT_DIR"/*.feature 2>/dev/null | sed 's/.*\//   /' || echo "   No feature files generated"
    fi
else
    echo ""
    echo "❌ Enhanced generation failed!"
    exit 1
fi 