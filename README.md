# AI-Driven Test Automation Framework

An intelligent web automation framework that uses AI to understand and execute test cases from Excel files.

## Features

- **AI-Powered Test Execution**: Uses LLM to understand and execute test steps
- **Video Recording**: Records test execution with high-quality video output
- **Excel Integration**: Reads test cases from Excel files
- **Smart Element Detection**: Enhanced DOM analysis for reliable element location
- **Test Case Filtering**: Run specific test cases or all tests
- **Visual Highlighting**: Red squares and yellow highlights for better test visibility

## Visual Highlighting Behavior (PROTECTED)

The framework includes specific visual highlighting behaviors that are **PROTECTED** and should not be modified:

### Assert Actions
- **Yellow background** with **red border** for found text
- **Bold text** for better visibility
- **Counter display** showing number of occurrences
- **Smooth scrolling** to show all highlights

### Click Actions  
- **Red border** (5px solid red) with **glow effect**
- **Floating "CLICKING HERE" label** with animation
- **Smooth transitions** for visual appeal

### Type Actions
- **Light yellow background** (#ffffcc) with **red border**
- **Temporary highlighting** that fades after 3 seconds

**⚠️ IMPORTANT**: These highlighting behaviors are protected and should not be changed without explicit user approval. See `test_framework/modules/highlight_protection.py` for detailed documentation.

## 📚 Documentation

For comprehensive information about using and developing with this framework, please refer to our detailed documentation:

### 📖 [User Guide](docs/USER_GUIDE.md)
**Everything you need to get started and use the framework effectively**
- Installation and setup instructions
- Writing tests in Gherkin and Excel formats
- Running tests and interpreting results
- Troubleshooting common issues
- Best practices and optimization tips

### 🔧 [Technical Guide](docs/TECHNICAL_GUIDE.md)
**Deep dive into the framework's architecture and technical implementation**
- System architecture and core components
- Step intent engine and pattern recognition
- Performance optimization strategies
- CI/CD integration and Xray setup
- Development and customization guidelines

### 📚 [Development History](docs/DEVELOPMENT_HISTORY.md)
**Project evolution, improvements, and lessons learned**
- Major architectural changes and milestones
- Performance optimization journey
- Problem-solving case studies
- Quality improvements and metrics
- Future roadmap and vision

## Quick Start

### Prerequisites

1. **Python 3.8+** with virtual environment
2. **FFmpeg** for video processing (optional)
3. **Web browser** (Chrome/Chromium recommended)

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd web-automation-reg

# Create and activate virtual environment
python3 -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Install Playwright browsers
playwright install chromium
```

### Running Tests

```bash
# Run Gherkin tests (recommended)
./run_gherkin.sh

# Run specific test cases
./run_gherkin.sh @TEST_RP-123

# Run with parallel execution (faster)
./run_scaled.sh -a

# Run Excel tests (legacy)
./run.sh @TC1
```

## 🎯 Key Features Highlights

### 🚀 **Ultra-Fast Parallel Execution**
- **Sequential Mode**: 2-3 tests/minute
- **Parallel Mode**: 8-12 tests/minute (4 workers)
- **CI Mode**: 15-20 tests/minute
- **77% performance improvement** over traditional execution

### 🧠 **Intelligent Step Recognition**
- Natural language test step parsing
- Support for English and Indonesian languages
- Context-aware element detection
- Automatic fallback strategies

### 📊 **Comprehensive Reporting**
- **Unified HTML reports** with tabbed navigation (parallel execution)
- **Individual test reports** with step-by-step details (sequential execution)
- **JUnit XML** for CI/CD integration
- **Xray-compatible** test results
- **Video recordings** of test execution

### 🔧 **Flexible Test Formats**
- **Gherkin/BDD**: Human-readable feature files
- **Excel**: Traditional spreadsheet format
- **Dual Support**: Use both formats simultaneously

For detailed information on installation, usage, troubleshooting, and advanced features, please refer to the comprehensive documentation linked above.

## 🚀 Quick Example

### Gherkin Test Example
```gherkin
@TEST_RP-123 @regression_test
Feature: Search Functionality

  Scenario: Search for training programs
    Given the user is on the homepage
    When I type "Pelatihan" in the search box
    And I click the search button
    Then I should see "Pelatihan" in the results
```

### Running the Example
```bash
# Run the specific test
./run_gherkin.sh @TEST_RP-123

# Run with parallel execution for better performance
./run_scaled.sh @TEST_RP-123
```

## 📊 Performance Metrics

| Execution Mode | Speed | Use Case |
|----------------|-------|----------|
| Sequential | 2-3 tests/min | Development & Debugging |
| Parallel | 8-12 tests/min | Regular Testing |
| CI Mode | 15-20 tests/min | Continuous Integration |

## 🎯 Getting Help

- **📖 User Guide**: Complete setup and usage instructions
- **🔧 Technical Guide**: Architecture and development details
- **📚 Development History**: Project evolution and lessons learned
- **🐛 Issues**: Check existing issues or create new ones
- **💬 Discussions**: Community support and feature requests

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License. 