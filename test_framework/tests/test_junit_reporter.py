#!/usr/bin/env python3
"""
Test script for JUnit Reporter
Verifies that the JUnit XML generation works correctly for Xray integration
"""

import sys
import os

# Add the test_framework directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    from modules.junit_reporter import JUnitReporter
    print("✅ JUnit Reporter imported successfully")
except ImportError as e:
    print(f"❌ Failed to import JUnit Reporter: {e}")
    sys.exit(1)

def test_junit_reporter():
    """Test the JUnit reporter functionality"""
    print("\n🧪 Testing JUnit Reporter...")
    
    # Create JUnit reporter
    reporter = JUnitReporter()
    
    # Start test suite
    reporter.start_test_suite("test-suite")
    
    # Test case 1: Passed test
    print("📝 Adding passed test case...")
    reporter.add_test_result(
        scenario_tags=['@TEST_RP-553', '@staging', '@regression_test'],
        scenario_name='Test Case 1: Successful Login',
        status='passed',
        duration=5.2,
        steps=[
            {'step_num': 1, 'status': 'passed', 'text': 'Navigate to login page'},
            {'step_num': 2, 'status': 'passed', 'text': 'Enter credentials'},
            {'step_num': 3, 'status': 'passed', 'text': 'Click login button'}
        ],
        error_message='',
        test_case_id='TC1'
    )
    
    # Test case 2: Failed test
    print("📝 Adding failed test case...")
    reporter.add_test_result(
        scenario_tags=['@TEST_RP-554', '@staging', '@ui_test'],
        scenario_name='Test Case 2: Failed Registration',
        status='failed',
        duration=3.8,
        steps=[
            {'step_num': 1, 'status': 'passed', 'text': 'Navigate to registration page'},
            {'step_num': 2, 'status': 'failed', 'text': 'Fill registration form', 'error': 'Element not found'},
            {'step_num': 3, 'status': 'skipped', 'text': 'Submit form'}
        ],
        error_message='Element not found: registration form',
        test_case_id='TC2'
    )
    
    # Test case 3: Skipped test
    print("📝 Adding skipped test case...")
    reporter.add_test_result(
        scenario_tags=['@TEST_RP-555', '@production', '@smoke_test'],
        scenario_name='Test Case 3: Skipped Payment Test',
        status='skipped',
        duration=0.1,
        steps=[
            {'step_num': 1, 'status': 'skipped', 'text': 'Navigate to payment page'},
            {'step_num': 2, 'status': 'skipped', 'text': 'Enter payment details'},
            {'step_num': 3, 'status': 'skipped', 'text': 'Process payment'}
        ],
        error_message='',
        test_case_id='TC3'
    )
    
    # End test suite
    reporter.end_test_suite()
    
    # Generate XML
    print("🔗 Generating JUnit XML...")
    xml_content = reporter.generate_xml()
    
    # Save XML file
    xml_path = reporter.save_xml("test_junit.xml")
    print(f"💾 JUnit XML saved to: {xml_path}")
    
    # Get summary
    summary = reporter.get_summary()
    print(f"\n📊 Test Summary:")
    print(f"   Total Tests: {summary['total_tests']}")
    print(f"   Passed: {summary['passed']}")
    print(f"   Failed: {summary['failed']}")
    print(f"   Errors: {summary['errors']}")
    print(f"   Skipped: {summary['skipped']}")
    print(f"   Success Rate: {summary['success_rate']:.1f}%")
    
    # Display XML preview
    print(f"\n📋 JUnit XML Preview (first 20 lines):")
    lines = xml_content.split('\n')
    for i, line in enumerate(lines[:20]):
        print(f"   {i+1:2d}: {line}")
    
    if len(lines) > 20:
        print(f"   ... ({len(lines) - 20} more lines)")
    
    print("\n✅ JUnit Reporter test completed successfully!")
    return True

def verify_xml_structure():
    """Verify that the generated XML has the correct structure for Xray"""
    print("\n🔍 Verifying XML structure for Xray compatibility...")
    
    try:
        import xml.etree.ElementTree as ET
        
        # Parse the generated XML
        tree = ET.parse('test_junit.xml')
        root = tree.getroot()
        
        # Check root element
        if root.tag != 'testsuites':
            print("❌ Root element should be 'testsuites'")
            return False
        
        # Check test suite
        testsuite = root.find('testsuite')
        if testsuite is None:
            print("❌ No testsuite element found")
            return False
        
        # Check test cases
        testcases = testsuite.findall('testcase')
        if len(testcases) != 3:
            print(f"❌ Expected 3 test cases, found {len(testcases)}")
            return False
        
        # Check properties for Xray integration
        for i, testcase in enumerate(testcases):
            properties = testcase.find('properties')
            if properties is None:
                print(f"❌ Test case {i+1} missing properties element")
                continue
            
            # Check for test_key property
            test_key_prop = properties.find("property[@name='test_key']")
            if test_key_prop is None:
                print(f"❌ Test case {i+1} missing test_key property")
            else:
                print(f"✅ Test case {i+1} has test_key: {test_key_prop.get('value')}")
            
            # Check for environment property
            env_prop = properties.find("property[@name='environment']")
            if env_prop is None:
                print(f"❌ Test case {i+1} missing environment property")
            else:
                print(f"✅ Test case {i+1} has environment: {env_prop.get('value')}")
        
        print("✅ XML structure verification completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ XML verification failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting JUnit Reporter Test")
    print("=" * 50)
    
    try:
        # Test JUnit reporter
        if test_junit_reporter():
            # Verify XML structure
            verify_xml_structure()
            
            print("\n🎉 All tests passed! JUnit Reporter is working correctly.")
            print("\n📁 Generated files:")
            print("   - test_junit.xml (JUnit XML for Xray)")
            print("   - test_framework/reports/ (HTML reports)")
            
        else:
            print("\n❌ JUnit Reporter test failed!")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
