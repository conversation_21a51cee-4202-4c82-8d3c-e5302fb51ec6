import re
import json

# Pattern matching only - no AI imports needed
OLLAMA_AVAILABLE = False
OllamaLLM = None

class LLMInterpreter:
    def __init__(self, model_name="pattern_matching"):
        # Pattern matching
        print("✅ Using pattern matching")
        self.llm = None

        self.url_pattern = re.compile(r'https?://[^\s]+')

    def extract_base_url(self, step):
        """Extract base URL from step if present."""
        match = self.url_pattern.search(step)
        if match:
            url = match.group(0)
            # Clean up URL if needed (remove trailing characters)
            if url[-1] in ['.', ',', ')', ']', '"', "'"]:
                url = url[:-1]
            return url
        return None

    def interpret_step(self, step, page_context=None):
        """Convert natural language step to actionable instructions."""
        # First, check for common patterns directly without using LLM
        step_lower = step.lower()

        # Handle navigation steps
        if "buka halaman" in step_lower or "go to" in step_lower or "navigate to" in step_lower:
            url = self.extract_base_url(step)
            if url:
                print(f"Direct pattern match: navigate to {url}")
                return {
                    "action_type": "navigate",
                    "target_description": "",
                    "value": "",
                    "url": url
                }

        # Handle typing steps - check for patterns like "isi", "ketik", "masukkan", "type"
        if any(pattern in step_lower for pattern in ["isi", "ketik", "masukkan", "type", "input"]):
            # Extract the field name and value
            field_match = re.search(r'(?:kolom|field|input|form)\s+(["\']?)([^"\']+)\1', step, re.IGNORECASE)
            value_match = re.search(r'(?:dengan|with)\s+(?:kata\s+)?["\']([^"\']+)["\']', step, re.IGNORECASE)

            # Special handling for search fields
            if "pencarian" in step_lower or "search" in step_lower:
                field = "pencarian"  # Default field name for search
            else:
                field = "search"  # Default field name
                if field_match:
                    field = field_match.group(2)
                    # Clean up field name if it contains "dengan kata"
                    if "dengan kata" in field.lower():
                        field = field.split("dengan kata")[0].strip()

            value = value_match.group(1) if value_match else ""

            if not value:  # Try another pattern if the first one didn't work
                value_match = re.search(r'["\']([^"\']+)["\']', step)
                value = value_match.group(1) if value_match else ""

                # If still no value, try to extract it from the end of the step
                if not value and "dengan" in step_lower:
                    value_part = step_lower.split("dengan")[-1].strip()
                    if value_part:
                        # Remove quotes if present
                        value = value_part.strip('"\'')

            print(f"Direct pattern match: type '{value}' into field '{field}'")
            return {
                "action_type": "type",
                "target_description": field,
                "value": value,
                "url": ""
            }

        # Handle click steps
        if any(pattern in step_lower for pattern in ["klik", "click", "tekan", "press"]):
            # Extract the target element
            target_match = re.search(r'(?:tombol|button|link|menu|tab|element|elemen)\s+["\']([^"\']+)["\']', step, re.IGNORECASE)
            if not target_match:
                # Try another pattern if the first one didn't work
                target_match = re.search(r'["\']([^"\']+)["\']', step)

            target = target_match.group(1) if target_match else ""

            # Special handling for search buttons
            if ("cari" in step_lower or "search" in step_lower) and not target:
                target = "Cari"

            print(f"Direct pattern match: click on '{target}'")
            return {
                "action_type": "click",
                "target_description": target,
                "value": "",
                "url": ""
            }

        # Handle assertion steps
        if any(pattern in step_lower for pattern in ["pastikan", "verify", "assert", "check"]):
            # Extract the text to verify (text between quotes)
            match = re.search(r'"([^"]*)"', step)
            if match:
                text_to_verify = match.group(1)
            else:
                # Try to extract text after "teks" or "text"
                match = re.search(r'(?:teks|text)\s+["\']([^"\']+)["\']', step, re.IGNORECASE)
                if match:
                    text_to_verify = match.group(1)
                else:
                    # Try to find any quoted text
                    match = re.search(r'["\']([^"\']+)["\']', step)
                    text_to_verify = match.group(1) if match else ""

                    # If still no text, try to extract it after "muncul"
                    if not text_to_verify and "muncul" in step_lower:
                        parts = step_lower.split("muncul")
                        if len(parts) > 1:
                            text_part = parts[1].strip()
                            # Remove leading "di" if present
                            if text_part.startswith("di "):
                                text_part = text_part[3:].strip()
                            text_to_verify = text_part

            print(f"Direct pattern match: assert text '{text_to_verify}'")
            return {
                "action_type": "assert",
                "target_description": text_to_verify,
                "value": "",
                "url": ""
            }

        # Handle scroll steps
        if "scroll" in step_lower:
            # Extract target text if specified (e.g., "scroll ke bawah sampai pada teks 'Semangat Rumah Pendidikan'")
            target = ""
            if "sampai pada teks" in step_lower or "until text" in step_lower:
                # Try to extract text in quotes
                text_match = re.search(r"['\"]([^'\"]+)['\"]", step)
                if text_match:
                    target = text_match.group(1)
                else:
                    # Fallback: extract text after "teks" or "text"
                    if "teks" in step_lower:
                        parts = step_lower.split("teks")
                        if len(parts) > 1:
                            target = parts[1].strip().strip("'\"")
                    elif "text" in step_lower:
                        parts = step_lower.split("text")
                        if len(parts) > 1:
                            target = parts[1].strip().strip("'\"")
            elif "bawah" in step_lower or "bottom" in step_lower:
                target = "bottom"
            
            print(f"Direct pattern match: scroll to {target or 'down'}")
            return {
                "action_type": "scroll",
                "target_description": target,
                "value": "",
                "url": ""
            }

        # If no direct pattern match, try using the LLM
        try:
            prompt = PromptTemplate(
                input_variables=["step", "context"],
                template="""
                You are an AI test automation expert. Convert this test step into a structured action plan.
                The test step may be written in English or Bahasa Indonesia.

                Test step: {step}

                Page context: {context}

                Analyze the step and determine the appropriate action:
                - For navigation ("buka halaman", "go to"): use action_type "navigate" with the URL
                - For clicking elements ("klik", "tekan", "click"): use action_type "click" with the element description
                - For typing text ("isi", "ketik", "masukkan", "type"): use action_type "type" with the field description and value
                - For assertions/verification ("pastikan", "verify", "assert"): use action_type "assert" with the text to verify
                - For scrolling ("scroll", "gulir"): use action_type "scroll" with the direction or target
                - For waiting ("tunggu", "wait"): use action_type "wait" with what to wait for

                Return a JSON object with these fields:
                - action_type: (navigate|click|type|select|assert|wait|scroll)
                - target_description: (description of the element to interact with)
                - value: (any value to enter, if applicable)
                - url: (URL to navigate to, if applicable)

                Examples:
                - "Go to https://example.com" → {"action_type": "navigate", "url": "https://example.com"}
                - "Buka halaman https://contoh.com" → {"action_type": "navigate", "url": "https://contoh.com"}
                - "Click on Login button" → {"action_type": "click", "target_description": "Login"}
                - "Klik tombol Masuk" → {"action_type": "click", "target_description": "Masuk"}
                - "Isi kolom pencarian dengan kata 'Pelatihan'" → {"action_type": "type", "target_description": "pencarian", "value": "Pelatihan"}
                - "Type 'Training' in search field" → {"action_type": "type", "target_description": "search", "value": "Training"}
                - "Pastikan teks 'Selamat Datang' muncul" → {"action_type": "assert", "target_description": "Selamat Datang"}
                - "Verify text 'Welcome' appears" → {"action_type": "assert", "target_description": "Welcome"}

                IMPORTANT: Return a single JSON object, not a list of objects.
                """
            )

            context = page_context or "No page context available yet."
            response = self.llm.invoke(prompt.format(step=step, context=context))

            # Parse response to extract JSON
            json_str = response.strip()
            # Handle case where model might wrap JSON in ```json blocks
            if "```json" in json_str:
                json_str = json_str.split("```json")[1].split("```")[0].strip()
            elif "```" in json_str:
                json_str = json_str.split("```")[1].split("```")[0].strip()

            result = json.loads(json_str)

            # Handle case where the model returns a list instead of a dict
            if isinstance(result, list) and len(result) > 0:
                print(f"Warning: Model returned a list instead of a dict. Using first item.")
                return result[0]

            return result
        except Exception as e:
            print(f"Error using LLM to interpret step: {e}")
            # Fallback to a simple action based on the step
            if "http" in step:
                return {
                    "action_type": "navigate",
                    "target_description": "",
                    "value": "",
                    "url": self.extract_base_url(step) or ""
                }
            else:
                return {
                    "action_type": "click",
                    "target_description": step,
                    "value": "",
                    "url": ""
                }
    def get_raw_completion(self, prompt):
        """Get a raw completion from the LLM without parsing."""
        try:
            response = self.llm.invoke(prompt)
            return response
        except Exception as e:
            print(f"Error getting raw completion from LLM: {e}")
            return "{}"
    async def aask(self, prompt):
        """Asynchronous version of ask method."""
        try:
            response = self.llm.invoke(prompt)
            return response
        except Exception as e:
            print(f"Error in aask: {e}")
            return f"Error: {str(e)}"
