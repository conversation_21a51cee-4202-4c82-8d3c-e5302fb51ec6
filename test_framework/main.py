#!/usr/bin/env python3
import os
import sys
import argparse
import time

# Set TOKENIZERS_PARALLELISM environment variable to avoid warnings
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# Add the current directory to Python path to allow relative imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from modules.test_reader import TestReader
    from modules.test_executor import TestExecutor
    from modules.recorder import Recorder
    from modules.gherkin_reader import GherkinReader
    from modules.junit_reporter import JUnitReporter
    # AI modules removed - pattern matching mode only
except ImportError:
    # Try with full path
    try:
        from test_framework.modules.test_reader import TestReader
        from test_framework.modules.test_executor import TestExecutor
        from test_framework.modules.recorder import Recorder
        from test_framework.modules.gherkin_reader import GherkinReader
        from test_framework.modules.junit_reporter import JUnitReporter
    except ImportError:
        # Add parent directory to path and try again
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        sys.path.insert(0, parent_dir)
        from test_framework.modules.test_reader import TestReader
        from test_framework.modules.test_executor import TestExecutor
        from test_framework.modules.recorder import Recorder
        from test_framework.modules.gherkin_reader import GherkinReader
        from test_framework.modules.junit_reporter import JUnitReporter
    # AI modules removed - pattern matching mode only

def main():
    # Check for dependencies
    Recorder.check_dependencies()

    # Check for macOS Homebrew issue
    import sys
    import os
    if sys.platform == 'darwin':
        brew_services_path = '/opt/homebrew/share/zsh/site-functions/_brew_services'
        if not os.path.exists(brew_services_path):
            print(f"Note: The file {brew_services_path} does not exist. This is normal if you haven't installed Homebrew or its services.")

    parser = argparse.ArgumentParser(description="Test Automation Framework")
    parser.add_argument("--excel", help="Path to Excel test file")
    parser.add_argument("--features", help="Path to Gherkin feature files directory")
    parser.add_argument("--output", default="./output", help="Output directory for recordings")
    parser.add_argument("--model", default="pattern_matching", help="Model to use (default: pattern_matching)")
    parser.add_argument("--convert-to-indonesian", action="store_true", help="Convert Gherkin steps to Indonesian (default: keep original)")
    parser.add_argument("--browser", default="chromium", choices=["chromium", "firefox", "webkit"],
                        help="Browser to use for testing (default: chromium)")
    parser.add_argument("--test-cases", help="Comma-separated list of test case IDs to run (e.g., TC1,TC2,TC3). If not specified, runs all test cases.")
    parser.add_argument("--parallel-mode", action="store_true", help="Enable parallel execution mode for faster performance")
    parser.add_argument("--single-report", action="store_true", help="Generate only a single comprehensive report instead of individual reports")

    args = parser.parse_args()

    # If no test source specified, show help
    if not args.excel and not args.features:
        parser.print_help()
        return

    print(f"Starting Test Automation Framework")
    
    # Determine test source and read test cases
    if args.features:
        print(f"Using Gherkin feature files from: {args.features}")
        print(f"Output directory: {args.output}")
        print(f"Model: {args.model}")
        
        # Check if features directory exists
        if not os.path.exists(args.features):
            print(f"ERROR: Features directory not found: {args.features}")
            return
            
        # Prepare requested test cases for filtering
        requested_tc_ids = None
        if args.test_cases:
            requested_tc_ids = [tc.strip() for tc in args.test_cases.split(',')]
            print(f"Requested test cases: {requested_tc_ids}")
            
        # Read test cases from Gherkin files
        print("Reading test cases from Gherkin feature files...")
        gherkin_reader = GherkinReader(args.features, not args.convert_to_indonesian)
        test_cases, base_urls = gherkin_reader.get_test_cases(requested_tc_ids)
        
        # Extract scenario names from Gherkin test cases
        scenario_names = {}
        for tc_id, tc_data in test_cases.items():
            scenario_names[tc_id] = tc_data.get('scenario_name', f"Test Case {tc_id}")
        
    else:  # Excel mode
        print(f"Excel file: {args.excel}")
        print(f"Output directory: {args.output}")
        print(f"Model: {args.model}")
        
        # Check if Excel file exists
        if not os.path.exists(args.excel):
            print(f"ERROR: Excel file not found: {args.excel}")
            return
            
        # Read test cases from Excel
        print("Reading test cases from Excel file...")
        test_reader = TestReader(args.excel)
        test_cases, base_urls, scenario_names = test_reader.get_test_cases()

    if not test_cases:
        print("No test cases found. Exiting.")
        return

    # Create output directory if it doesn't exist
    if not os.path.exists(args.output):
        print(f"Creating output directory: {args.output}")
        os.makedirs(args.output)

    # For Excel mode, filter test cases if specific test cases are requested
    if not args.features and args.test_cases:
        requested_test_cases = [tc.strip() for tc in args.test_cases.split(',')]
        filtered_test_cases = {}
        filtered_base_urls = {}
        
        for tc_id in requested_test_cases:
            if tc_id in test_cases:
                filtered_test_cases[tc_id] = test_cases[tc_id]
                filtered_base_urls[tc_id] = base_urls.get(tc_id, "")
            else:
                print(f"Warning: Test case '{tc_id}' not found in Excel file. Available test cases: {list(test_cases.keys())}")
        
        if not filtered_test_cases:
            print("Error: None of the requested test cases were found. Exiting.")
            return
        
        test_cases = filtered_test_cases
        base_urls = filtered_base_urls
        print(f"Filtered to {len(test_cases)} test case(s): {list(test_cases.keys())}")

    # Initialize a single report generator for all test cases (only if not in single report mode)
    report_generator = None
    if not args.single_report:
        try:
            from modules.report_generator import HTMLReportGenerator
        except ImportError:
            try:
                from test_framework.modules.report_generator import HTMLReportGenerator
            except ImportError:
                # Add parent directory to path and try again
                parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                sys.path.insert(0, parent_dir)
                from test_framework.modules.report_generator import HTMLReportGenerator

        report_generator = HTMLReportGenerator()
        report_generator.start_test_run()
    
    # Collect all test results
    all_test_results = []
    
    # Check if parallel mode is enabled
    if args.parallel_mode:
        print("🚀 Parallel mode enabled - using optimized execution")
        # For parallel mode, we'll use the optimized parallel executor
        # The parallel executor will handle the execution and reporting
        # Continue with normal execution for individual test cases
        print("✅ Parallel mode detected - continuing with test execution")
    
    # Execute each test case sequentially
    for tc_id, tc_data in test_cases.items():
        print(f"Executing test case: {tc_id}")

        # Get the base URL for this test case
        base_url = base_urls.get(tc_id, "")
        print(f"Base URL: {base_url}")

        # Get scenario name and steps based on data structure
        if isinstance(tc_data, dict):
            # Gherkin mode: tc_data is a dictionary with 'steps' and 'scenario_name'
            scenario_name = tc_data.get('scenario_name', f"Test Case {tc_id}")
            tc_steps = tc_data.get('steps', [])
        else:
            # Excel mode: tc_data is a list of steps
            scenario_name = scenario_names.get(tc_id, f"Test Case {tc_id}")
            tc_steps = tc_data

        # Clean up the steps - remove the "Go to URL." prefix if base URL is provided
        cleaned_steps = []
        for step in tc_steps:
            if base_url and ("Go to " in step and ". " in step):
                # If we have a base URL and the step starts with "Go to", extract just the action part
                cleaned_step = step.split(". ", 1)[1]
                cleaned_steps.append(cleaned_step)
            else:
                cleaned_steps.append(step)

        print(f"Steps: {cleaned_steps}")

        recorder = Recorder(output_dir=args.output, test_id=tc_id)
        # Extract scenario tags from feature file
        scenario_tags = []
        try:
            features_dir = args.features if args.features else "test_framework/features"
            if os.path.exists(features_dir):
                for file in os.listdir(features_dir):
                    if file.endswith('.feature'):
                        with open(os.path.join(features_dir, file), 'r', encoding='utf-8') as f:
                            content = f.read()
                            # Extract tags from feature file
                            import re
                            tag_matches = re.findall(r'@\w+', content)
                            scenario_tags.extend(tag_matches)
                            break  # Use first feature file found
        except Exception as e:
            print(f"Warning: Could not extract tags: {e}")
        
        executor = TestExecutor(
            steps=cleaned_steps,
            recorder=recorder,
            model_name=args.model,
            base_url=base_url,  # Pass the base URL
            report_generator=report_generator,  # Pass report generator (None if single_report mode)
            ci_mode=args.parallel_mode,  # Enable CI mode for performance optimizations when in parallel mode
            test_case_id=tc_id,  # Pass the test case ID
            scenario_tags=scenario_tags,  # Pass scenario tags for Xray integration
            scenario_name=scenario_name  # Pass scenario name for Xray integration
        )

        # Create and run an event loop to execute the async function
        import asyncio
        asyncio.run(executor.run())
        
        # Collect test results for the report
        test_result = {
            'tc_id': tc_id,
            'scenario_name': scenario_name,
            'steps': cleaned_steps,
            'step_results': executor.test_results,
            'base_url': base_url
        }
        all_test_results.append(test_result)

    # Generate final comprehensive report (only if not in single report mode)
    if not args.single_report and report_generator:
        report_generator.end_test_run()
    
        # Create feature data from all test results
        feature_name = "Test Execution"

        # Find the actual feature file
        feature_file = "rumdik_regression_test.feature"  # Default
        relative_path = "test_framework/features/rumdik_regression_test.feature"  # Default

        try:
            features_dir = args.features if args.features else "test_framework/features"
            if os.path.exists(features_dir):
                for file in os.listdir(features_dir):
                    if file.endswith('.feature'):
                        feature_file = file
                        relative_path = f"{features_dir}/{file}"
                        break
        except Exception as e:
            print(f"Warning: Could not find feature file: {e}")

        # Extract tags from executed test cases
        tags = []
        try:
            # Get tags from executed test cases (e.g., @TC1, @TC2)
            for test_result in all_test_results:
                tc_id = test_result['tc_id']
                tags.append(f"@{tc_id}")

            # Also extract additional tags from feature files if available
            features_dir = args.features if args.features else "test_framework/features"
            if os.path.exists(features_dir):
                for file in os.listdir(features_dir):
                    if file.endswith('.feature'):
                        with open(os.path.join(features_dir, file), 'r', encoding='utf-8') as f:
                            content = f.read()
                            # Extract tags from feature file
                            import re
                            tag_matches = re.findall(r'@\w+', content)
                            tags.extend(tag_matches)
                            # Remove duplicates
                            tags = list(set(tags))
                            break  # Use first feature file found
        except Exception as e:
            print(f"Warning: Could not extract tags: {e}")

        # Create scenarios from all test results
        scenarios = []
        for test_result in all_test_results:
            # Clean scenario name to remove TC part (e.g., "Pencarian (TC1)" -> "Pencarian")
            scenario_name = test_result["scenario_name"]
            # Remove TC pattern from scenario name - now handles both (TC1) and @TC1 formats
            import re
            scenario_name = re.sub(r'\s*\(TC\d+\)', '', scenario_name).strip()
            scenario_name = re.sub(r'\s*@TC\d+', '', scenario_name).strip()

            scenario_data = {
                'name': f'Test Scenario: [{test_result["tc_id"]}] {scenario_name}',
                'ticket_number': test_result['tc_id'],
                'status': 'passed' if all(r.get('status') == 'passed' for r in test_result['step_results']) else 'failed',
                'steps': []
            }

            # Add steps to scenario
            for i, step in enumerate(test_result['steps']):
                step_result = test_result['step_results'][i] if i < len(test_result['step_results']) else {'status': 'undefined'}
                step_data = {
                    'text': step,
                    'status': step_result.get('status', 'undefined'),
                    'details': f"Step {i+1} execution",
                    'error': step_result.get('error', ''),
                    'type': 'step'
                }
                scenario_data['steps'].append(step_data)

            scenarios.append(scenario_data)

        # Add feature to report
        report_generator.add_feature_result(
            feature_name=feature_name,
            feature_file=feature_file,
            relative_path=relative_path,
            scenarios=scenarios,
            tags=tags
        )

        # Save the final comprehensive report
        report_path = report_generator.save_report()
        print(f"\n📊 Comprehensive HTML Report generated: {report_path}")

    # Generate JUnit XML for Xray integration (only if not in single report mode)
    if not args.single_report:
        print("\n🔗 Generating JUnit XML for Xray integration...")
        junit_reporter = JUnitReporter()

        # Start test suite - use features directory name or default
        suite_name = os.path.basename(args.features) if args.features else "Test Suite"
        junit_reporter.start_test_suite(suite_name)

        # Read feature files to extract tags for each scenario
        feature_scenario_tags = {}
        if args.features and os.path.exists(args.features):
            print("📖 Reading feature files to extract scenario tags...")
            for file in os.listdir(args.features):
                if file.endswith('.feature'):
                    file_path = os.path.join(args.features, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()

                            # Extract feature name
                            feature_match = re.search(r'Feature:\s*(.+?)(?:\n|$)', content)
                            feature_name = feature_match.group(1).strip() if feature_match else os.path.splitext(file)[0]

                            # Extract scenarios and their tags
                            scenario_blocks = re.findall(r'@([^@\n]+)\s*\n\s*Scenario[^:]*:\s*(.+?)(?=\n\s*@|\n\s*Feature:|$)', content, re.DOTALL)

                            for tags, scenario_name in scenario_blocks:
                                # Clean up tags and scenario name
                                tag_list = [tag.strip() for tag in tags.split('@') if tag.strip()]
                                clean_scenario = scenario_name.strip()

                                # Map scenario to tags
                                feature_scenario_tags[clean_scenario] = {
                                    'tags': tag_list,
                                    'feature_name': feature_name,
                                    'file_name': file
                                }

                    except Exception as e:
                        print(f"Warning: Could not read feature file {file}: {e}")

        # Add all test results to JUnit reporter
        for test_result in all_test_results:
            # Extract tags from test case
            scenario_tags = []
            feature_name = None
            classname = None

            try:
                # Get tags from executed test cases
                tc_id = test_result['tc_id']
                scenario_tags.append(f"@{tc_id}")

                # Find matching scenario in feature files to get proper tags
                scenario_name = test_result["scenario_name"]
                clean_scenario_name = re.sub(r'\s*\(TC\d+\)', '', scenario_name).strip()
                clean_scenario_name = re.sub(r'\s*@TC\d+', '', clean_scenario_name).strip()

                # Look for matching scenario in feature files
                for scenario, info in feature_scenario_tags.items():
                    if clean_scenario_name.lower() in scenario.lower() or scenario.lower() in clean_scenario_name.lower():
                        scenario_tags.extend([f"@{tag}" for tag in info['tags']])
                        feature_name = info['feature_name']
                        classname = info['file_name']
                        break

                # If no match found, try to extract from any feature file content
                if not feature_name and args.features:
                    for file in os.listdir(args.features):
                        if file.endswith('.feature'):
                            with open(os.path.join(args.features, file), 'r', encoding='utf-8') as f:
                                content = f.read()
                                if clean_scenario_name.lower() in content.lower():
                                    # Extract feature name from this file
                                    feature_match = re.search(r'Feature:\s*(.+?)(?:\n|$)', content)
                                    if feature_match:
                                        feature_name = feature_match.group(1).strip()
                                        classname = file
                                    break

            except Exception as e:
                print(f"Warning: Could not extract tags for JUnit: {e}")

            # Clean scenario name
            scenario_name = test_result["scenario_name"]
            import re
            scenario_name = re.sub(r'\s*\(TC\d+\)', '', scenario_name).strip()
            scenario_name = re.sub(r'\s*@TC\d+', '', scenario_name).strip()

            # Determine test status
            test_status = 'passed' if all(r.get('status') == 'passed' for r in test_result['step_results']) else 'failed'

            # Calculate execution time (if available)
            execution_time = 0.0
            if 'execution_time' in test_result:
                execution_time = test_result['execution_time']

            # Add test result to JUnit reporter
            junit_reporter.add_test_result(
                scenario_tags=scenario_tags,
                scenario_name=f"[{test_result['tc_id']}] {scenario_name}",
                status=test_status,
                duration=execution_time,
                steps=test_result['step_results'],
                error_message='\n'.join([r.get('error', '') for r in test_result['step_results'] if r.get('status') == 'failed']),
                test_case_id=test_result['tc_id'],
                feature_name=feature_name,
                classname=classname
            )

        # End test suite and generate JUnit XML
        junit_reporter.end_test_suite()

        # Save JUnit XML file
        junit_path = junit_reporter.save_xml("junit.xml")
        print(f"📋 JUnit XML Report generated: {junit_path}")

        # Print JUnit summary
        summary = junit_reporter.get_summary()
        print(f"📊 JUnit Summary: {summary['total_tests']} tests, {summary['passed']} passed, {summary['failed']} failed, {summary['success_rate']:.1f}% success rate")

    print("Test execution completed.")

if __name__ == "__main__":
    main()
    # Make sure there's no code after this that might be executing commands
