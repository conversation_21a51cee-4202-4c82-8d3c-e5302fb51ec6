"""
Security Utilities

This module provides utility functions for security testing.
"""

import re
from urllib.parse import urlparse

def is_valid_url(url: str) -> bool:
    """Check if a URL is valid.
    
    Args:
        url: URL to check
        
    Returns:
        True if the URL is valid, False otherwise
    """
    if not url or not isinstance(url, str):
        return False
    
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc])
    except Exception:
        return False

def is_safe_protocol(url: str) -> bool:
    """Check if a URL uses a safe protocol.
    
    Args:
        url: URL to check
        
    Returns:
        True if the URL uses a safe protocol, False otherwise
    """
    if not is_valid_url(url):
        return False
    
    try:
        result = urlparse(url)
        return result.scheme in ["http", "https"]
    except Exception:
        return False

def has_dangerous_extension(url: str) -> bool:
    """Check if a URL has a dangerous file extension.
    
    Args:
        url: URL to check
        
    Returns:
        True if the URL has a dangerous file extension, False otherwise
    """
    if not is_valid_url(url):
        return False
    
    try:
        result = urlparse(url)
        path = result.path.lower()
        dangerous_extensions = [".exe", ".dll", ".bat", ".cmd", ".sh", ".js", ".vbs"]
        return any(path.endswith(ext) for ext in dangerous_extensions)
    except Exception:
        return False

def contains_malicious_pattern(url: str) -> bool:
    """Check if a URL contains a malicious pattern.
    
    Args:
        url: URL to check
        
    Returns:
        True if the URL contains a malicious pattern, False otherwise
    """
    if not is_valid_url(url):
        return False
    
    malicious_patterns = [
        r"(?:javascript|data|vbscript|file):",
        r"(?:onload|onerror|onmouseover|onclick|onmouseout|onfocus|onblur)=",
        r"<(?:script|iframe|object|embed|applet|meta|link)",
        r"(?:alert|confirm|prompt)\s*\(",
        r"(?:eval|setTimeout|setInterval|Function)\s*\("
    ]
    
    for pattern in malicious_patterns:
        if re.search(pattern, url, re.IGNORECASE):
            return True
    
    return False

def detect_xss(content: str) -> bool:
    """Detect XSS in content.
    
    Args:
        content: Content to check
        
    Returns:
        True if XSS is detected, False otherwise
    """
    xss_patterns = [
        r"<script[^>]*>.*?</script>",
        r"<img[^>]*on\w+\s*=",
        r"javascript:",
        r"<[^>]*\s+on\w+\s*=",
        r"<[^>]*\s+dynsrc\s*=",
        r"<[^>]*\s+lowsrc\s*=",
        r"<[^>]*\s+style\s*=[^>]*expression\s*\(",
        r"<[^>]*\s+style\s*=[^>]*url\s*\(",
        r"<[^>]*\s+style\s*=[^>]*@import",
        r"<[^>]*\s+style\s*=[^>]*behavior\s*:",
        r"<[^>]*\s+style\s*=[^>]*-moz-binding\s*:",
        r"<[^>]*\s+formaction\s*=",
        r"<[^>]*\s+xlink:href\s*=",
        r"<[^>]*\s+href\s*=\s*['\"]javascript:",
        r"<[^>]*\s+action\s*=\s*['\"]javascript:",
        r"<[^>]*\s+data\s*=\s*['\"]data:"
    ]
    
    for pattern in xss_patterns:
        if re.search(pattern, content, re.IGNORECASE):
            return True
    
    return False

def detect_sql_injection(content: str) -> bool:
    """Detect SQL injection in content.
    
    Args:
        content: Content to check
        
    Returns:
        True if SQL injection is detected, False otherwise
    """
    sql_injection_patterns = [
        r"(?:'\s*OR\s*'?\d+'?='?\d+)",
        r"(?:'\s*OR\s*'?[a-zA-Z]+'?='?[a-zA-Z]+)",
        r"(?:'\s*OR\s*'?[\w]+'?='?[\w]+)",
        r"(?:'\s*OR\s*'?[\w]+'?=[\w]+)",
        r"(?:'\s*OR\s*'?[\w]+'?=[\w]+--)",
        r"(?:'\s*OR\s*'?[\w]+'?=[\w]+#)",
        r"(?:'\s*OR\s*'?[\w]+'?=[\w]+\/\*)",
        r"(?:'\s*OR\s*'?[\w]+'?=[\w]+\/\*.*?\*\/)",
        r"(?:'\s*OR\s*'?[\w]+'?=[\w]+--.*?$)",
        r"(?:'\s*OR\s*'?[\w]+'?=[\w]+#.*?$)",
        r"(?:'\s*OR\s*'?[\w]+'?=[\w]+;)",
        r"(?:'\s*OR\s*'?[\w]+'?=[\w]+;\s*--)",
        r"(?:'\s*OR\s*'?[\w]+'?=[\w]+;\s*#)",
        r"(?:'\s*OR\s*'?[\w]+'?=[\w]+;\s*\/\*)",
        r"(?:'\s*OR\s*'?[\w]+'?=[\w]+;\s*\/\*.*?\*\/)",
        r"(?:'\s*OR\s*'?[\w]+'?=[\w]+;\s*--.*?$)",
        r"(?:'\s*OR\s*'?[\w]+'?=[\w]+;\s*#.*?$)",
        r"(?:'\s*OR\s*'?[\w]+'?=[\w]+;\s*;)",
        r"(?:'\s*OR\s*'?[\w]+'?=[\w]+;\s*;\s*--)",
        r"(?:'\s*OR\s*'?[\w]+'?=[\w]+;\s*;\s*#)",
        r"(?:'\s*OR\s*'?[\w]+'?=[\w]+;\s*;\s*\/\*)",
        r"(?:'\s*OR\s*'?[\w]+'?=[\w]+;\s*;\s*\/\*.*?\*\/)",
        r"(?:'\s*OR\s*'?[\w]+'?=[\w]+;\s*;\s*--.*?$)",
        r"(?:'\s*OR\s*'?[\w]+'?=[\w]+;\s*;\s*#.*?$)",
        r"(?:'\s*OR\s*'?[\w]+'?=[\w]+;\s*;\s*;)",
        r"(?:'\s*OR\s*'?[\w]+'?=[\w]+;\s*;\s*;\s*--)",
        r"(?:'\s*OR\s*'?[\w]+'?=[\w]+;\s*;\s*;\s*#)",
        r"(?:'\s*OR\s*'?[\w]+'?=[\w]+;\s*;\s*;\s*\/\*)",
        r"(?:'\s*OR\s*'?[\w]+'?=[\w]+;\s*;\s*;\s*\/\*.*?\*\/)",
        r"(?:'\s*OR\s*'?[\w]+'?=[\w]+;\s*;\s*;\s*--.*?$)",
        r"(?:'\s*OR\s*'?[\w]+'?=[\w]+;\s*;\s*;\s*#.*?$)",
        r"(?:'\s*OR\s*'?[\w]+'?=[\w]+;\s*;\s*;\s*;)"
    ]
    
    for pattern in sql_injection_patterns:
        if re.search(pattern, content, re.IGNORECASE):
            return True
    
    return False

def detect_command_injection(content: str) -> bool:
    """Detect command injection in content.
    
    Args:
        content: Content to check
        
    Returns:
        True if command injection is detected, False otherwise
    """
    command_injection_patterns = [
        r"(?:;|&|\||\n|\r|\$\(|\`)\s*(?:ls|dir|cat|type|more|less|head|tail|nl|od|hexdump|xxd|strings|grep|find|locate|whereis|which|who|w|id|groups|users|last|lastlog|lastb|lastcomm|finger|pinky|lsof|netstat|ss|ps|top|htop|kill|pkill|killall|pgrep|pstree|watch|nohup|screen|tmux|bg|fg|jobs|disown|nice|renice|ionice|taskset|chrt|time|timeout|env|printenv|set|export|unset|echo|printf|read|cat|tac|nl|od|hexdump|xxd|base64|uuencode|uudecode|crypt|mkpasswd|openssl|gpg)",
        r"(?:;|&|\||\n|\r|\$\(|\`)\s*(?:ssh|scp|sftp|ftp|telnet|nc|netcat|ncat|socat|curl|wget|lynx|links|w3m|aria2c|axel|git|svn|hg|bzr|darcs|rsync|rcp|scp|dd|tar|gzip|bzip2|xz|zip|unzip|7z|ar|cpio|rpm|dpkg|apt|apt-get|aptitude|yum|dnf|pacman|zypper|emerge|pkg|brew|port|snap|flatpak)",
        r"(?:;|&|\||\n|\r|\$\(|\`)\s*(?:systemctl|service|rc-service|rc-update|update-rc.d|chkconfig|launchctl|start|stop|restart|reload|status|enable|disable|mask|unmask|journalctl|dmesg|logger|wall)"
    ]
    
    for pattern in command_injection_patterns:
        if re.search(pattern, content, re.IGNORECASE):
            return True
    
    return False
