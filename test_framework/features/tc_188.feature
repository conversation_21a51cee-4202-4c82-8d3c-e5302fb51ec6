Feature: TC 188
  As a user
  I want to access tc 188 functionality
  So that I can perform tc 188 operations

  base_url: https://rumah-baru.staging.belajar.id/

  @TEST_RP-932 @regression_test @positive_test @staging
  Scenario: Aktifasi Layanan Layanan UKBI pada Ruang Bahasa
    Given the user is on the Ruang Bahasa page
    When the user sees the Layanan UKBI option
    When I click "Layanan UKBI"
    Then the user should be redirected to the Layanan UKBI page "https://ukbi.kemendikdasmen.go.id/"


  @TEST_RP-549 @about-us @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Viewing the blue button entry point on homepage
    Given the user is on the homepage of Rumah Pendidikan
    When I scroll to "Semangat Rumah Pendidikan" segment
    Then I should see a blue button labeled "Pelajari Selengkapnya"

  @TEST_RP-547 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Navigating to the next page of articles
    Given the user is on the "Informasi Untuk Anda" listing page
    When I click "Lihat lebih banyak" button
    Then I click "Artikel"
    And I wait for the page to load
    And I should see the next set of articles listed

  @TEST_RP-543 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Viewing the list of "Informasi Untuk Anda"
    Given the user is on the homepage or news section of Rumah Pendidikan
    When the page loads successfully
    Then I should see a list of available "Informasi Untuk Anda" displayed with thumbnails and titles

	@TEST_RP-375 @manual @positive_test @rumah-pendidikan @staging @web
	Scenario: User views "Ruang Pemerintah" and accesses them without opening a new tab
		Given the user is on the Rumah Pendidikan homepage
		When the user navigates to "Ruang Pemerintah" section
		Then the user should see the following content and list of services in the "Ruang Pemerintah":
		      | Layanan pemerintah daerah untuk mengelola sumber daya sekolah hingga evaluasi pendidikan     |
		      | Layanan yang Tersedia                                                                        |
		      | Akun Pendidikan                                                                              |
		      | Neraca Pendidikan Daerah                                                                     |
		      | Rapor Pendidikan Daerah                                                                      |
		      | Manajemen Aplikasi Rencana Kegiatan dan Anggaran Sekolah                                     |
		When the user clicks on any of the listed services
		Then the system should redirect the user to the corresponding service page
		And the redirection should happen in the same browser tab

  @TEST_RP-374 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: User views list of "Ruang Publik" and accesses them without opening a new tab
    Given the user accesses the Rumah Pendidikan homepage
    Then the user should see the following "Ruang Publik" items:
    And | Service Name |
    And | Informasi dan materi pendidikan |
    And | Layanan yang Tersedia |
    And | Bantuan Pendidikan |
    And | Informasi Data Pendidikan |
    And | Kursus Digital |
    And | Layanan Informasi dan Pengaduan |
    And | Majalah Pendidikan |
    And | Pusat Perbukuan |
    When I click on any of the listed services
    Then the system should redirect the user to the selected service
    And the redirection should happen in the same browser tab

  @TEST_RP-373 @fe @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: User views "Ruang GTK" and accesses them without opening a new tab
    Given the user is on the Rumah Pendidikan homepage
    Then the user should see the following content and service list in the "Ruang GTK" section:
    And | Sumber inspirasi peningkatan kompetensi serta kinerja Guru dan Tenaga Kependidikan (GTK) |
    And | Layanan yang Tersedia |
    And | Diklat |
    And | Sertifikasi Pendidik |
    And | Pelatihan Mandiri |
    And | Komunitas |
    And | Pengelolaan Kinerja |
    And | Seleksi Kepala Sekolah |
    And | Refleksi Kompetensi |
    And | Perangkat Ajar |
    And | CP/ATP |
    And | Ide Praktik |
    And | Bukti Karya |
    And | Video Inspirasi |
    And | Asesmen (Asesmen Murid dan AKM Kelas) |
    And | Kelas |
    And | Dokumen Rujukan Pengelolaan Kinerja |
    And | Dokumen Rujukan Pengelolaan Pembelajaran |
    And | Dokumen Rujukan Pengelolaan Satuan Pendidikan |
    And | Dokumen Rujukan Peningkatan Kompetensi |
    When I click on any of the listed services
    Then the system should redirect the user to the corresponding service page
    And the redirection should happen in the same browser tab

  @TEST_RP-372 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: User views "Ruang Orang Tua" and accesses them without opening a new tab
    Given the user is on the Rumah Pendidikan homepage
    Then the user should see the following text and service options under the Parent Space section:
    And | Sarana partisipasi orang tua melalui pantauan capaian Murid |
    And | dan dukungan belajar di rumah |
    And | Layanan yang Tersedia |
    And | Konsultasi Pendidikan |
    And | Layanan Informasi dan Pengaduan |
    And | Panduan Pendampingan |
    When I click on any of the listed services
    Then the system should redirect the user to the selected service
    And the redirection should happen in the same browser tab

  @TEST_RP-370 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: User views homepage structure and interacts with service cards and search results
    Given the user accesses the Rumah Pendidikan homepage
    Then the user should see the following components in order from top to bottom:
    And | Header with logo, search box, and login button |
    And | Hero banner with copytext (static image, unclickable) |
    And | 8 room icons with segment titles |
    And | 4 live photos with text "Semangat Rumah Pendidikan Nasional 2025" |
    And | Carousel of 5 featured services with segment titles |
    And | 3 education actors' testimonials about Rumah Pendidikan |
    And | FAQ section with button linking to the complaint form |
    And | Footer with logo, navigation, and links to Google Play Store & App Store |
    Then I should see the content
    When I click a service card within a room page
    Then the user should be redirected to the respective service platform in the same browser tab
    When the user performs a search with keywords such as "guru" or "murid"
    Then the system should display matching service cards containing the keyword in the title, description, or tags

  @TEST_RP-357 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Show 'No results found' state when there are no matching search results
    Given the user is on the Rumah Pendidikan homepage
    When I enters a keyword in the search box that does not match any service title or description
    Then the system should display a message stating "Tidak Ada Hasil yang Sesuai"

  @TEST_RP-356 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Show list of services matching the keyword entered by the user
    Given the user is on the Rumah Pendidikan homepage
    When I enters a keyword in the search box "Pelatihan"
    Then a list of matching services should be displayed "Pelatihan" to the user

  @TEST_RP-355 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Display Relawan Pendidikan Pages
    Given the user is on the Rumah Pendidikan homepage
    When I click "Ruang Mitra"
    When I click "Relawan Pendidikan" on the "Ruang Mitra"
    Then user direct to Relawan Pendidikan Pages "https://rumah.pendidikan.go.id/relawan-pendidikan.html"

  @TEST_RP-351 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Access Ruang Orang Tua from Icon
    Given the user is on the Rumah Pendidikan homepage
    When I scroll to the section
    And user click "Ruang Orang Tua"
    Then user direct to Ruang Orang Tua Pages "https://rumah-baru.staging.belajar.id/ruang/orang-tua"

  @TEST_RP-350 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Access Ruang Publik  from Icon
    Given user is on the Rumah Pendidikan homepage
    When I click Icon "Ruang Publik"
    Then user direct to Ruang Publik Pages "https://rumah-baru.staging.belajar.id/ruang/publik"

  @TEST_RP-349 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Access Ruang Mitra from Icon
    Given user is on the Rumah Pendidikan homepage
    When I click Icon "Ruang Mitra"
    Then user direct to Ruang Mitra "https://rumah-baru.staging.belajar.id/ruang/mitra"

  @TEST_RP-348 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Access Ruang Pemerintah from Icon
    Given user is on the Rumah Pendidikan homepage
    When I click Icon "Ruang Pemerintah"
    Then user direct to Ruang Pemerintah Pages "https://rumah-baru.staging.belajar.id/ruang/pemerintah"

  @TEST_RP-347 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Access Ruang Bahasa from Icon
    Given user is on the Rumah Pendidikan homepage
    When I click Icon "Ruang Bahasa"
    Then user direct to Ruang Bahasa Pages "https://rumah-baru.staging.belajar.id/ruang/bahasa"

  @TEST_RP-346 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Access Ruang Sekolah from Icon
    Given user is on the Rumah Pendidikan homepage
    When I click Icon "Ruang Sekolah"
    Then user direct to Ruang Sekolah Pages "https://rumah-baru.staging.belajar.id/ruang/sekolah"

  @TEST_RP-345 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Access Ruang Murid from Icon
    Given user is on the Rumah Pendidikan homepage
    When I click Icon "Ruang Murid"
    Then user direct to Ruang Murid Pages "https://rumah-baru.staging.belajar.id/ruang/murid"

  @TEST_RP-344 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Access Ruang GTK from Icon
    Given user is on the Rumah Pendidikan homepage
    When I click Icon "Ruang GTK"
    Then user direct to Ruang GTK Pages "https://rumah-baru.staging.belajar.id/ruang/gtk"

  @TEST_RP-342 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Display Tittle and Deskription Rumah Pendidikan
    Given user is on the Rumah Pendidikan homepage
    When user scrolls to the middle page
    Then user should see The Title and Description of Web "Semangat Rumah Pendidikan"

  @TEST_RP-340 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Access Native Apps Android on Footer
    Given the user is on the Rumah Pendidikan homepage
    When I scroll to the footer
    And user click Google Play Store Logo
    Then user direct to Native Apps Rumah Pendidikan Android

  @TEST_RP-339 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Access Native Apps IOS on Footer
    Given the user is on the Rumah Pendidikan homepage
    When I scroll to the footer
    And user click Apps Store Logo
    Then user direct to Native Apps Rumah Pendidikan IOS

  @TEST_RP-338 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Access "Kebijakan Privasi" on Footer
    Given the user is on the Rumah Pendidikan homepage
    When I scroll to the footer
    And user click "Kebijakan Privasi"
    Then user direct to Kebijakan Privasi Pages "https://rumah-baru.staging.belajar.id/kebijakan-privasi"

  @TEST_RP-337 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Access "Syarat & Ketentuan" on Footer
    Given the user is on the Rumah Pendidikan homepage
    When I scroll to the footer
    And user click "Syarat & Ketentuan"
    Then user direct to Syarat & Ketentuan Pages "https://rumah-baru.staging.belajar.id/syarat-dan-ketentuan"

  @TEST_RP-336 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Access "Pusat Bantuan Rumah Pendidikan" on Footer
    Given the user is on the Rumah Pendidikan homepage
    When I scroll to the footer
    And user click "Pusat Bantuan Rumah Pendidikan"
    Then user direct to Pusat Bantuan Rumah Pendidikan Pages "https://pengaduan.ult.kemendikdasmen.go.id/hc/en-gb/requests/new"

  @TEST_RP-335 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Access Ruang Orang Tua on Footer
    Given the user is on the Rumah Pendidikan homepage
    When I scroll to the section "Jelajahi Ruang di Rumah Pendidikan"
    And user click "Ruang Orang Tua"
    Then user direct to Ruang Orang Tua Pages "https://rumah-baru.staging.belajar.id/ruang/orang-tua"

  @TEST_RP-334 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Access Ruang Publik on Footer
    Given the user is on the Rumah Pendidikan homepage
    When I scroll to the section "Jelajahi Ruang di Rumah Pendidikan"
    And user click "Ruang Publik"
    Then user direct to Ruang Publik Pages "https://rumah-baru.staging.belajar.id/ruang/publik"

  @TEST_RP-333 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Access Ruang Mitra on Footer
    Given the user is on the Rumah Pendidikan homepage
    When I scroll to the section "Jelajahi Ruang di Rumah Pendidikan"
    And user click "Ruang Mitra"
    Then user direct to Ruang Mitra "https://rumah-baru.staging.belajar.id/ruang/mitra"

  @TEST_RP-332 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Access Ruang Pemerintah on Footer
    Given the user is on the Rumah Pendidikan homepage
    When I scroll to the section "Jelajahi Ruang di Rumah Pendidikan"
    And user click "Ruang Pemerintah"
    Then user direct to Ruang Pemerintah Pages "https://rumah-baru.staging.belajar.id/ruang/pemerintah"

  @TEST_RP-331 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Access Ruang Bahasa on Footer
    Given the user is on the Rumah Pendidikan homepage
    When I scroll to the section "Jelajahi Ruang di Rumah Pendidikan"
    And user click "Ruang Bahasa"
    Then user direct to Ruang Bahasa Pages "https://rumah-baru.staging.belajar.id/ruang/bahasa"

  @TEST_RP-330 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Access Ruang Sekolah on Footer
    Given the user is on the Rumah Pendidikan homepage
    When I scroll to the section "Jelajahi Ruang di Rumah Pendidikan"
    And user click "Ruang Sekolah"
    Then user direct to Ruang Sekolah Pages "https://rumah-baru.staging.belajar.id/ruang/sekolah"

  @TEST_RP-329 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Access Ruang Murid on Footer
    Given the user is on the Rumah Pendidikan homepage
    When I scroll to the section "Jelajahi Ruang di Rumah Pendidikan"
    And user click "Ruang Murid"
    Then user direct to Ruang Murid Pages "https://rumah-baru.staging.belajar.id/ruang/murid"

  @TEST_RP-328 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Access Ruang GTK on Footer
    Given the user is on the Rumah Pendidikan homepage
    When I scroll to the section "Jelajahi Ruang di Rumah Pendidikan"
    And user click "Ruang GTK"
    Then user direct to Ruang GTK Pages "https://rumah-baru.staging.belajar.id/ruang/gtk"

  @TEST_RP-327 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Display Footer Element on each pages
    Given the user is on the Rumah Pendidikan homepage
		  When the user scrolls to the Footer section
		  Then the user should see a Logo+Name of Kementrian on Footer
		  And the user should see Layanan Ruang Segmen on Footer
		  And the user should see Navigasi Segmen on Footer
		  And the user should see Entry Point to Native Apps Segmen on Footer

  @TEST_RP-309 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: User can view various frequently asked questions and their answers
    Given the user is on the "Rumah Pendidikan" homepage
    When I scroll to the "Pertanyaan yang paling sering ditanyakan" section
    Then I should see the questions and their answers
    When I clicks on a question
    Then the answer related to the selected question should be displayed on the screen

  @TEST_RP-307 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: User is redirected to the About Rumah Pendidikan page
    Given the user is on the Rumah Pendidikan homepage
    When I click "pelajari selengkapnya"
    Then the user should be redirected to the "Mengenal Rumah Pendidikan" page
    Then And the page should display complete information about Rumah Pendidikan

  @TEST_RP-305 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: User can log in and log out using Google Account
    Given the user is on the homepage
    When I click "masuk"
    And selects the "Masuk" option
    And in the page https://rumah-baru.staging.belajar.id/login i click "Masuk"
    And in the column email i input user_email_guru
    And i click Next
    And in the column password i input user_password_guru
    And i click Next
    And i click "Continue" 
    Then the user should be logged in
    Then And the navbar on the top right should display the user's initial name "TE" on the right
    When I click the user's initial name "TE"
    And selects "Logout"
    Then the user should be logged out
    Then And the navbar on the top right should display the "Masuk" button again

  @TEST_RP-302 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Clicking "Masuk" redirects to login page
    Given the user is on the homepage
    When I click "masuk"
    Then the user should be redirected to the login page

  @TEST_RP-301 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: User can enter any keyword in the search box
    Given the user is on the homepage
    When I enter text "Pelatihan" on the search box
    Then the search box should display the entered text "Pelatihan"

  @TEST_RP-300 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Clicking logo redirects to homepage
    Given the user is on any page of "Rumah Pendidikan"
    When I click the "Rumah Pendidikan" logo
    Then the user should be redirected to the homepage

  @TEST_RP-299 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Display of header elements on homepage
    Given the user navigates to "https://rumah-baru.staging.belajar.id/"
    Then the header should display the "Rumah Pendidikan" logo on the left
    And a search box on the top
    And a "Masuk" button on the right

  @TEST_RP-296 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Footer Link to Terms and Conditions
    Given the user navigates to "https://rumah-baru.staging.belajar.id/"
    When I scroll to the footer
    When I click on the "Syarat & Ketentuan"
    Then the user should be redirected to the "Syarat dan Ketentuan" page

  @TEST_RP-295 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Footer Link to Privacy Policy
    Given the user navigates to "https://rumah-baru.staging.belajar.id/"
    When I scroll to the footer
    When I click on the "Kebijakan Privasi"
    Then the user should be redirected to the "Kebijakan Privasi" page

  @TEST_RP-148 @regression_test @negative_test @rumah-pendidikan @staging @web
  Scenario: Gagal Diarahkan ke halaman Webview Website Layanan Informasi dan Pengaduan
    Given Mengakses halaman Beranda pada aplikasi Rumah Pendidikan
    When Klik Ruang Orang Tua
    And Menampilkan halaman Ruang Orang Tua
    And Klik "Layanan Informasi dan Pengaduan" 
    Then Tidak diarahkan ke Webview Website "https://ult.kemdikbud.go.id/"
    And Gagal Diarahkan ke halaman Webview Website Layanan Informasi dan Pengaduan

  @TEST_RP-142 @regression_test @negative_test @rumah-pendidikan @staging @web
  Scenario: Gagal Diarahkan ke halaman Webview Website BUKU
    Given Mengakses halaman Beranda pada aplikasi Rumah Pendidikan
    When Klik Ruang Publik
    And Menampilkan halaman Ruang Publik
    And Klik "Bantuan Pendidikan"
    Then Tidak diarahkan ke Webview Website "https://pip.kemdikbud.go.id/"
    And Gagal Diarahkan ke halaman Webview Website BUKU

  @TEST_RP-138 @regression_test @negative_test @rumah-pendidikan @staging @web
  Scenario: Gagal Diarahkan ke halaman Webview BUKU
    Given Mengakses halaman Beranda pada aplikasi Rumah Pendidikan
    When Klik Ruang Publik
    And Menampilkan halaman Ruang Publik
    And Klik "Pusat Perbukuan"
    Then Tidak diarahkan ke Webview Website "https://buku.kemdikbud.go.id/"
    And Gagal Diarahkan ke halaman Webview Website BUKU

  @TEST_RP-124 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Gagal Diarahkan ke halaman Webview Website Belajar.id
    Given Mengakses halaman Beranda pada aplikasi Rumah Pendidikan
    When Klik "Ruang Pemerintah"
    And Menampilkan halaman "Ruang Pemerintah"
    And Klik "Akun Pendidikan"
    Then diarahkan ke Webview Website "https://belajar.id/"
    And Diarahkan ke halaman Webview Website Belajar.id

  @TEST_RP-113 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Berhasil Diarahkan ke halaman Webview Website UKBI
    Given Mengakses halaman Beranda pada aplikasi Rumah Pendidikan
    When Klik "Ruang Bahasa"
    And Menampilkan halaman "Ruang Bahasa"
    And Klik "Layanan UKBI"
    Then Akan diarahkan ke Webview Website "https://ukbi.kemdikbud.go.id/"
    And Berhasil Diarahkan ke halaman Webview Website UKBI

  @TEST_RP-112 @regression_test @negative_test @rumah-pendidikan @staging @web
  Scenario: Gagal Diarahkan ke halaman Webview Website Penerjemahan
    Given Mengakses halaman Beranda pada aplikasi Rumah Pendidikan
    When Klik "Ruang Bahasa"
    And Menampilkan halaman "Ruang Bahasa"
    And Klik "Penerjemahan Daring" 
    Then Tidak diarahkan ke Webview Website "https://penerjemahan.kemdikbud.go.id/"
    And Gagal Diarahkan ke halaman Webview Website Penerjemahan

  @TEST_RP-102 @regression_test @negative_test @rumah-pendidikan @staging @web
  Scenario: Gagal Diarahkan ke halaman Webview Website Arkas
    Given Mengakses halaman Beranda pada aplikasi Rumah Pendidikan
    When Klik "Ruang Sekolah"
    And Menampilkan halaman "Ruang Sekolah"
    And Klik "Rencana Kegiatan dan Belanja Sekolah" 
    Then Tidak diarahkan ke Webview Website "https://arkas.kemdikbud.go.id/"
    And Gagal Diarahkan ke halaman Webview Website Arkas

  @TEST_RP-101 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Berhasil Diarahkan ke halaman Webview Website Arkas
    Given Mengakses halaman Beranda pada aplikasi Rumah Pendidikan
    When Klik "Ruang Sekolah"
    And Menampilkan halaman Ruang Sekolah
    And Klik "Rencana Kegiatan dan Belanja Sekolah"
    Then Akan diarahkan ke Webview Website "https://arkas.kemendikdasmen.go.id/download/arkas4"
    And Berhasil Diarahkan ke halaman Webview Website Arkas

  @TEST_RP-100 @regression_test @negative_test @rumah-pendidikan @staging @web
  Scenario: Gagal Diarahkan ke halaman Webview Website Rapor Pendidikan
    Given Mengakses halaman Beranda pada aplikasi Rumah Pendidikan
    When Klik "Ruang Sekolah"
    And Menampilkan halaman "Ruang Sekolah"
    And Klik "Rapor Satuan Pendidikan" 
    Then Tidak diarahkan ke Webview Website "https://raporpendidikan.kemdikbud.go.id/app"
    And Gagal Diarahkan ke halaman Webview Website Rapor Pendidikan

  @TEST_RP-98 @regression_test @negative_test @rumah-pendidikan @staging @web
  Scenario: Gagal Diarahkan ke halaman Webview Website Sekolah Kita
    Given Mengakses halaman Beranda pada aplikasi Rumah Pendidikan
    When Klik "Ruang Sekolah"
    And Menampilkan halaman "Ruang Sekolah"
    And Klik "Profil Sekolah" 
    Then Tidak diarahkan ke Webview "https://sekolah.data.kemendikdasmen.go.id/"
    And Gagal Diarahkan ke halaman Webview Website Sekolah Kita

  @TEST_RP-83 @regression_test @positive_test @rumah-pendidikan @staging @web
  Scenario: Berhasil Searching
    Given Mengakses halaman Beranda pada aplikasi Rumah Pendidikan
    When Klik pada Search Bar
    And Ketik "Belajar"
    Then Sistem menampilkan hasil pencarian dengan kata kunci "Belajar"
    And Berhasil searching

  @TEST_RP-46 @fe @revamp @web
  Scenario: [Web][FE][Revamp][automation] Initial deployment to staging for web revamp
    Given the user has a web browser open
    Given When the user navigates to "https://rumah-baru.staging.belajar.id/"
    Then the website should be accessible