#!/bin/bash
# Activate virtual environment
source .venv/bin/activate

# Initialize variables
test_cases=""
other_args=""
model="pattern_matching"  # No AI model needed

# Process all arguments
test_case_list=""
for arg in "$@"; do
    if [[ $arg == @* ]]; then
        # Remove @ prefix and add to test case list
        test_case="${arg#@}"
        if [ -z "$test_case_list" ]; then
            test_case_list="$test_case"
        else
            test_case_list="$test_case_list,$test_case"
        fi
    else
        # Add to other arguments
        if [ -z "$other_args" ]; then
            other_args="$arg"
        else
            other_args="$other_args $arg"
        fi
    fi
done

# Set test_cases argument if we have any
if [ -n "$test_case_list" ]; then
    test_cases="--test-cases $test_case_list"
fi

echo "✅ Starting AI test framework in Gherkin mode"
echo "💡 No AI interaction - pattern matching only"
echo "📝 Using Gherkin feature files"

# Set environment variables for report generation
export USER="${USER:-$(whoami)}"
export RUNNER_MACHINE="${HOSTNAME:-$(hostname)}"
export CI_USER="${USER:-$(whoami)}"
export RUNNER_NAME="${HOSTNAME:-$(hostname)}"

# Record start time for duration calculation
START_TIME=$(date +%s)

# Run the test framework with Gherkin features
# Enable CI mode for faster execution
CI=true python test_framework/main.py --features test_framework/features --output ./test_framework/test_recordings --model $model $test_cases $other_args

# Calculate duration
END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))
echo "⏱️  Total execution time: ${DURATION} seconds" 